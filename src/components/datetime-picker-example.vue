<!-- @format -->
<template>
  <div class="datetime-picker-example">
    <h2>日期时间选择器示例</h2>
    
    <div class="demo-section">
      <h3>基础用法</h3>
      <div class="input-group">
        <label>选择的日期时间：</label>
        <input 
          type="text" 
          :value="formatDateTime(selectedDateTime)" 
          @click="openDateTimePicker" 
          readonly 
          placeholder="点击选择日期时间"
        />
      </div>
      <button @click="openDateTimePicker" class="btn-primary">打开日期时间选择器</button>
    </div>

    <div class="demo-section">
      <h3>自定义范围</h3>
      <div class="input-group">
        <label>选择的日期时间（2020-2030）：</label>
        <input 
          type="text" 
          :value="formatDateTime(customRangeDateTime)" 
          @click="openCustomRangePicker" 
          readonly 
          placeholder="点击选择日期时间"
        />
      </div>
      <button @click="openCustomRangePicker" class="btn-secondary">打开自定义范围选择器</button>
    </div>

    <!-- 基础日期时间选择器 -->
    <oeui-datetime-picker
      ref="datetimePicker"
      title="请选择日期时间"
      text="选择您需要的日期和时间"
      :current="selectedDateTime"
      @confirm="onDateTimeConfirm"
      @cancel="onDateTimeCancel"
    />

    <!-- 自定义范围日期时间选择器 -->
    <oeui-datetime-picker
      ref="customRangePicker"
      title="请选择日期时间"
      text="年份范围：2020-2030"
      :current="customRangeDateTime"
      :start-year="2020"
      :end-year="2030"
      @confirm="onCustomRangeConfirm"
      @cancel="onCustomRangeCancel"
    />
  </div>
</template>

<script>
import { ref, getCurrentInstance } from 'vue'
import oeuiDatetimePicker from '../oeui/datetime-picker.vue'

export default {
  name: 'DatetimePickerExample',
  components: {
    oeuiDatetimePicker
  },
  setup() {
    const { proxy } = getCurrentInstance()
    
    // 基础选择器的选中时间
    const selectedDateTime = ref(new Date())
    
    // 自定义范围选择器的选中时间
    const customRangeDateTime = ref(new Date(2025, 0, 1, 12, 0)) // 2025年1月1日 12:00
    
    // 格式化日期时间显示
    const formatDateTime = (date) => {
      if (!date) return ''
      
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}`
    }
    
    // 打开基础日期时间选择器
    const openDateTimePicker = () => {
      proxy.$refs.datetimePicker.open()
    }
    
    // 打开自定义范围日期时间选择器
    const openCustomRangePicker = () => {
      proxy.$refs.customRangePicker.open()
    }
    
    // 基础选择器确认回调
    const onDateTimeConfirm = (date) => {
      selectedDateTime.value = date
      console.log('选择的日期时间:', formatDateTime(date))
    }
    
    // 基础选择器取消回调
    const onDateTimeCancel = () => {
      console.log('取消选择日期时间')
    }
    
    // 自定义范围选择器确认回调
    const onCustomRangeConfirm = (date) => {
      customRangeDateTime.value = date
      console.log('选择的自定义范围日期时间:', formatDateTime(date))
    }
    
    // 自定义范围选择器取消回调
    const onCustomRangeCancel = () => {
      console.log('取消选择自定义范围日期时间')
    }
    
    return {
      selectedDateTime,
      customRangeDateTime,
      formatDateTime,
      openDateTimePicker,
      openCustomRangePicker,
      onDateTimeConfirm,
      onDateTimeCancel,
      onCustomRangeConfirm,
      onCustomRangeCancel
    }
  }
}
</script>

<style lang="scss" scoped>
.datetime-picker-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
  }
  
  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
    
    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 18px;
    }
    
    .input-group {
      margin-bottom: 15px;
      
      label {
        display: block;
        margin-bottom: 5px;
        color: #666;
        font-size: 14px;
      }
      
      input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        
        &:focus {
          outline: none;
          border-color: #ff6b9d;
        }
      }
    }
    
    .btn-primary, .btn-secondary {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .btn-primary {
      background-color: #ff6b9d;
      color: white;
      
      &:hover {
        background-color: #e55a8a;
      }
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
      
      &:hover {
        background-color: #5a6268;
      }
    }
  }
}
</style>
