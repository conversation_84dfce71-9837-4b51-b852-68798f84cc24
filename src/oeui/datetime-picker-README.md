# 日期时间选择器组件 (DateTime Picker)

基于现有 picker 组件改造的年月日时分选择器，支持滚轮式选择日期和时间。

## 功能特性

- 🗓️ 支持年、月、日、时、分五个维度的选择
- 📱 移动端友好的滚轮交互
- 🎨 与现有 UI 风格保持一致
- ⚡ 自动处理月份天数变化（如2月28/29天）
- 🔧 可自定义年份范围
- 📅 支持设置默认时间

## 组件位置

```
src/oeui/datetime-picker.vue
```

## 基础用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <button @click="openPicker">选择日期时间</button>
    
    <!-- 日期时间选择器 -->
    <oeui-datetime-picker
      ref="datetimePicker"
      title="请选择日期时间"
      :current="selectedDate"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
  </div>
</template>

<script>
import { ref, getCurrentInstance } from 'vue'
import oeuiDatetimePicker from '@/oeui/datetime-picker.vue'

export default {
  components: {
    oeuiDatetimePicker
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const selectedDate = ref(new Date())
    
    const openPicker = () => {
      proxy.$refs.datetimePicker.open()
    }
    
    const onConfirm = (date) => {
      selectedDate.value = date
      console.log('选择的日期时间:', date)
    }
    
    const onCancel = () => {
      console.log('取消选择')
    }
    
    return {
      selectedDate,
      openPicker,
      onConfirm,
      onCancel
    }
  }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '请选择日期时间' | 选择器标题 |
| text | String | '' | 选择器描述文本 |
| current | Date | new Date() | 当前选中的日期时间 |
| btnText | String | '确定' | 确认按钮文本 |
| startYear | Number | 1970 | 年份选择范围开始年份 |
| endYear | Number | 当前年份+10 | 年份选择范围结束年份 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| confirm | (date: Date) | 确认选择时触发，返回选中的日期时间对象 |
| cancel | - | 取消选择时触发 |

## 方法

| 方法名 | 说明 |
|--------|------|
| open() | 打开选择器 |
| close() | 关闭选择器 |

## 高级用法

### 自定义年份范围

```vue
<oeui-datetime-picker
  ref="picker"
  title="选择生日"
  :start-year="1950"
  :end-year="2010"
  :current="birthday"
  @confirm="onBirthdayConfirm"
/>
```

### 设置默认时间

```vue
<script>
export default {
  setup() {
    // 设置默认时间为今天下午2点
    const defaultTime = new Date()
    defaultTime.setHours(14, 0, 0, 0)
    
    const selectedDate = ref(defaultTime)
    
    return {
      selectedDate
    }
  }
}
</script>
```

### 格式化显示

```javascript
const formatDateTime = (date) => {
  if (!date) return ''
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hour = date.getHours().toString().padStart(2, '0')
  const minute = date.getMinutes().toString().padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}`
}
```

## 样式自定义

组件使用 SCSS 编写，主要的样式变量：

```scss
// 主色调
$primary-color: #ff6b9d;

// 文字颜色
$text-color: #333;
$text-light-color: #999;

// 背景色
$bg-color: #ffffff;
$mask-color: rgba(0, 0, 0, 0.45);
```

## 注意事项

1. 组件依赖 `wheel.vue` 滚轮组件，确保路径正确
2. 月份变化时会自动调整日期范围（如从31日切换到2月会自动调整到28/29日）
3. 组件使用 Vue 3 Composition API 编写
4. 确保在使用前正确引入组件

## 示例文件

完整的使用示例请参考：`src/components/datetime-picker-example.vue`

## 兼容性

- Vue 3.x
- 现代浏览器（支持 CSS3 和 ES6+）
- 移动端浏览器
